import fs from 'fs'
import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from '@tasks/common/tools'
import { RenewableEnergyTokenLogic } from '@/types/contracts/renewableEnergyToken/RenewableEnergyTokenLogic'

wrappedTask('restoreTokenIdsByAccountId', 'restore all account data', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  let tokensByAccountId: Array<any> = []

  const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${hre.network.name}/tokensbyaccount.json`, 'utf8')
  const isValid = md5Valid({ obj: data, item: 'tokensbyaccount', network: hre.network.name })
  tokensByAccountId = JSON.parse(data)

  if (!isValid) {
    throw new Error('The data is invalid or empty data.')
  }

  const { contract } = await getContractWithSigner<RenewableEnergyTokenLogic>({
    hre,
    contractName: 'RenewableEnergyTokenLogic',
  })

  console.log(`*** restore accounts data...`)
  console.log(`*** restore renewable energy token data...`)
  const sigPrams = await getBackupSignature({ hre, salt: 'setRETokensAll' })
  const limit = 1000

  while (tokensByAccountId.length > limit) {
    const receipt = await contract.restoreTokenIdsByAccountId(
      tokensByAccountId.slice(0, limit),
      sigPrams.deadline,
      sigPrams.sig,
    )
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
    tokensByAccountId = tokensByAccountId.slice(limit)
  }

  if (tokensByAccountId.length > 0) {
    const receipt = await contract.restoreTokenIdsByAccountId(tokensByAccountId, sigPrams.deadline, sigPrams.sig)
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  }
})

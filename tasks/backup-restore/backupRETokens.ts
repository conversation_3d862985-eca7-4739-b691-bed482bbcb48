import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { RenewableEnergyTokenLogic } from '@/types/contracts/renewableEnergyToken/RenewableEnergyTokenLogic'

wrappedTask('backupRETokens', 'backup all renewable token data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    const { contract } = await getContractWithSigner<RenewableEnergyTokenLogic>({
      hre,
      contractName: 'RenewableEnergyTokenLogic',
    })

    console.log(`*** backup renewable token data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getRETokensAll' })

    const tokens: Array<any> = []
    let offset = 0
    const limit = 1000

    const totalCount = await contract.getTokenCount()
    console.log(`Total item: ${totalCount.toString()}`)

    while (tokens.length != Number(totalCount)) {
      if (tokens.length > Number(totalCount)) {
        console.error(`Error: Accounts count ${tokens.length} is greater than total count`)
        break
      }

      // [result, count, error]
      const [result, , err] = await contract.backupRenewableEnergyTokens(offset, limit, sigPrams.deadline, sigPrams.sig)
      if (err != '') {
        console.log(`backup ${tokens.length + 1} ~ ${tokens.length + result.length} failed`)
        console.log('Error:', err)
        break
      } else {
        console.log(`backup ${tokens.length + 1} ~ ${tokens.length + result.length} items to local`)
        for (const key of Object.keys(result)) {
          const token = []
          for (const itemKey of Object.keys(result[key])) {
            const tokenItem = result[key][itemKey]
            if (typeof tokenItem === 'bigint') {
              token[itemKey] = tokenItem.toString()
            } else {
              token[itemKey] = tokenItem
            }
          }
          tokens.push(token)
        }
      }
      offset += limit
    }

    console.log(`All ${totalCount} (equle to the total count) items have been successfully backed up.`)

    await saveBackupToJson({ data: tokens, fileName: 'retokens', networkName: hre.network.name })
  },
)

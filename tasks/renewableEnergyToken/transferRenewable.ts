import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails, showEthersRes } from '@tasks/common/tools'
import { envVers } from '@/envVers'
import { RenewableEnergyTokenLogic } from '@/types/contracts/renewableEnergyToken/RenewableEnergyTokenLogic'

wrappedTask('transferRenewable', 'transfer renwable token to from account to account', {
  filePath: path.basename(__filename),
})
  .addParam('fromAccountId', 'from account id')
  .addParam('toAccountId', 'to account id')
  .addParam('tokenId', 'token id')
  .setAction(async (args, hre) => {
    const fromAccountId = convertToHex({ hre, value: args.fromAccountId || envVers.localTest.accountId1 })
    const toAccountId = convertToHex({ hre, value: args.toAccountId || envVers.localTest.accountId2 })
    const tokenId = convertToHex({ hre, value: args.tokenId || envVers.localTest.renewableId1 })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const params = {
      'From Account ID': fromAccountId,
      'To Account ID': toAccountId,
      'Token ID': tokenId,
      'Trace ID': traceId,
    }

    console.log(`** Input parameter Information **\n`)

    printTable({ data: params })

    const { contract } = await getContractWithSigner<RenewableEnergyTokenLogic>({
      hre,
      contractName: 'RenewableEnergyTokenLogic',
    })

    try {
      const receipt = await contract.transfer(fromAccountId, toAccountId, tokenId, traceId)

      console.log(`** renewableEnergyTokenLogic.transfer receipt Information **\n`)
      const res = await receipt.wait()
      showEthersRes({ res })
    } catch (error) {
      showErrorDetails({ error })
    }
  })

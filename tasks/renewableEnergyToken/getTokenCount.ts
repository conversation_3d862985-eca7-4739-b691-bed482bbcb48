import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from '@tasks/common/tools'
import { RenewableEnergyTokenLogic } from '@/types/contracts/renewableEnergyToken/RenewableEnergyTokenLogic'

wrappedTask('getTokenCount', 'get renewable token count', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    try {
      const { contract } = await getContractWithSigner<RenewableEnergyTokenLogic>({
        hre,
        contractName: 'RenewableEnergyTokenLogic',
      })

      const receipt = await contract.getTokenCount()

      const formattedReceipt = {
        tokenCount: receipt.toString(),
      }
      console.log(`** getTokenCount receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  },
)

import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable } from '@tasks/common/tools'
import { TokenStatus } from '@tasks/define/tokenStatusEnum'
import { envVers } from '@/envVers'

wrappedTask('getTokenList', 'get token list by account id', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator id')
  .addParam('accountId', 'account id')
  .addParam('offset', 'offset')
  .addParam('limit', 'limit')
  .addParam('sortOrder', 'sort order')
  .setAction(async (args, hre) => {
    const { ethers } = hre

    const validatorId = convertToHex({ hre, value: args.validatorId || envVers.localTest.valId })
    const accountId = convertToHex({ hre, value: args.accountId || envVers.localTest.accountId1 })
    const offset = args.offset || envVers.localTest.offset
    const limit = args.limit || envVers.localTest.limit
    const sortOrder = args.sortOrder || envVers.localTest.sortOrder

    console.log(`** getTokenList Parameters **\n`)
    const params = {
      validatorId: validatorId,
      accountId: accountId,
      offset: offset,
      limit: limit,
      sortOrder: sortOrder,
    }
    printTable({ data: params })

    const { contract } = await getContractWithSigner({
      hre,
      contractName: 'RenewableEnergyTokenLogic',
    })

    const [tokenList, totalCount, err] = await contract.getTokenList(validatorId, accountId, offset, limit, sortOrder)

    console.log(`** getTokenList receipt Information **\n`)

    if (err && err !== '') {
      printTable({ data: { error: err } })
      return
    }

    const fmtReceipt = tokenList.reduce(
      (acc, token, index) => {
        const baseKey = `Index:${index + 1}`
        acc[`${baseKey} - Token ID`] = ethers.decodeBytes32String(token.tokenId) || 'None'
        acc[`${baseKey} - Token Status`] = `${token.tokenStatus} (${TokenStatus[token.tokenStatus] || 'Unknown'})`
        acc[`${baseKey} - Metadata ID`] = ethers.decodeBytes32String(token.metadataId) || 'None'
        acc[`${baseKey} - Metadata Hash`] = ethers.decodeBytes32String(token.metadataHash) || 'None'
        acc[`${baseKey} - Mint Account ID`] = ethers.decodeBytes32String(token.mintAccountId) || 'None'
        acc[`${baseKey} - Owner Account ID`] = ethers.decodeBytes32String(token.ownerAccountId) || 'None'
        acc[`${baseKey} - Previous Account ID`] = ethers.decodeBytes32String(token.previousAccountId) || 'None'
        acc[`${baseKey} - Is Locked`] = token.isLocked
        return acc
      },
      { 'Total Count': totalCount },
    )

    printTable({ data: fmtReceipt })
  })

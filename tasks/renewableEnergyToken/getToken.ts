import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from '@tasks/common/tools'
import { TokenStatus } from '@tasks/define/tokenStatusEnum'
import { envVers } from '@/envVers'
import { RenewableEnergyTokenLogic } from '@/types/contracts/renewableEnergyToken/RenewableEnergyTokenLogic'

wrappedTask('getToken', 'get token by token id', { filePath: path.basename(__filename) })
  .addParam('tokenId', 'token id')
  .setAction(async (args, hre) => {
    const tokenId = convertToHex({ hre, value: args.tokenId || envVers.localTest.renewableId1 })

    console.log(`** getToken Parameters **\n`)
    const params = {
      tokenId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<RenewableEnergyTokenLogic>({
        hre,
        contractName: 'RenewableEnergyTokenLogic',
      })
      const { tokenStatus, metadataId, metadataHash, mintAccountId, ownerAccountId, previousAccountId, isLocked } = (
        await contract.getToken(tokenId)
      )[0]

      const formattedReceipt = {
        tokenStatus: tokenStatus + ' (' + TokenStatus[Number(tokenStatus)] + ')',
        metadataId: metadataId,
        metadataHash: metadataHash,
        mintAccountId: mintAccountId,
        ownerAccountId: ownerAccountId,
        previousAccountId: previousAccountId,
        isLocked: isLocked,
      }
      console.log(`** getToken receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })

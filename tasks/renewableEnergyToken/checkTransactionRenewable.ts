import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from '@tasks/common/tools'
import { envVers } from '@/envVers'
import { RenewableEnergyTokenLogic } from '@/types/contracts/renewableEnergyToken/RenewableEnergyTokenLogic'

wrappedTask('checkTransactionRenewable', 'checkTransaction by token id', { filePath: path.basename(__filename) })
  .addParam('sendAccountId', 'send account id')
  .addParam('fromAccountId', 'from account id')
  .addParam('toAccountId', 'to account id')
  .addParam('misc2', 'misc2')
  .setAction(async (args, hre) => {
    const sendAccountId = convertToHex({ hre, value: args.sendAccountId || envVers.localTest.accountId1 })
    const fromAccountId = convertToHex({ hre, value: args.fromAccountId || envVers.localTest.accountId1 })
    const toAccountId = convertToHex({ hre, value: args.toAccountId || envVers.localTest.accountId2 })

    console.log(`** checkTransaction Renewable Parameters **\n`)
    const params = {
      sendAccountId: sendAccountId,
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      misc1: convertToHex({ hre, value: 'renewable' }),
      misc2: String(args.misc2),
    }

    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<RenewableEnergyTokenLogic>({
        hre,
        contractName: 'RenewableEnergyTokenLogic',
      })

      const { success, err } = await contract.checkTransaction(
        sendAccountId,
        fromAccountId,
        toAccountId,
        params.misc1,
        params.misc2,
      )

      const formattedReceipt = {
        result: success,
        error: err.toString(),
      }
      console.log(`** checkTransaction Renewable receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })

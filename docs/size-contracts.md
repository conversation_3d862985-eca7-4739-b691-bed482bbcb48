Nothing to compile
No need to generate any newer typings.
 ·----------------------------------------|--------------------------------|--------------------------------·
 |  Solc version: 0.8.12                  ·  Optimizer enabled: true       ·  Runs: 200                     │
 ·········································|································|·································
 |  Contract Name                         ·  Deployed size (KiB) (change)  ·  Initcode size (KiB) (change)  │
 ·········································|································|·································
 |  IBCHost                               ·                 0.062 (0.000)  ·                 0.090 (0.000)  │
 ·········································|································|·································
 |  IBCModuleManager                      ·                 0.062 (0.000)  ·                 0.090 (0.000)  │
 ·········································|································|·································
 |  Error                                 ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Secp256k1                             ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  ECCMath                               ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Constant                              ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  MPTProof                              ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  RLPReader                             ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  CountersUpgradeable                   ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  AddressUpgradeable                    ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  StringsUpgradeable                    ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Strings                               ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Address                               ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  StringUtils                           ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Channel                               ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  ChannelCounterparty                   ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Height                                ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  ChannelIdentifiedChannel              ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  PacketState                           ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Packet                                ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  MerklePrefix                          ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Counterparty                          ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  ProtoBufRuntime                       ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IbcLightclientsIbft2V1Header          ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IbcLightclientsIbft2V1ConsensusState  ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Version                               ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IbcLightclientsIbft2V1ClientState     ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  ConnectionEnd                         ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IbcLightclientsMockV1ClientState      ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IbcLightclientsMockV1ConsensusState   ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IbcLightclientsMockV1Header           ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  GoogleProtobufAny                     ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  ECDSAUpgradeable                      ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  Math                                  ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  SignedMath                            ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  SafeMathUpgradeable                   ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  ECDSA                                 ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  MathUpgradeable                       ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  SignedMathUpgradeable                 ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IBCHeight                             ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IBCClientLib                          ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IBCCommitment                         ·                 0.084 (0.000)  ·                 0.138 (0.000)  │
 ·········································|································|·································
 |  IBCChannelLib                         ·                 0.349 (0.000)  ·                 0.405 (0.000)  │
 ·········································|································|·································
 |  IBCConnectionLib                      ·                 0.391 (0.000)  ·                 0.447 (0.000)  │
 ·········································|································|·································
 |  Migrations                            ·                 0.419 (0.000)  ·                 0.468 (0.000)  │
 ·········································|································|·································
 |  ICS20Lib                              ·                 0.454 (0.000)  ·                 0.511 (0.000)  │
 ·········································|································|·································
 |  IBCMockLib                            ·                 0.598 (0.000)  ·                 0.654 (0.000)  │
 ·········································|································|·································
 |  TransferableMock2                     ·                 1.907 (0.000)  ·                 1.938 (0.000)  │
 ·········································|································|·································
 |  TransferableMock3                     ·                 1.907 (0.000)  ·                 1.938 (0.000)  │
 ·········································|································|·································
 |  TransferableMock1                     ·                 1.907 (0.000)  ·                 1.938 (0.000)  │
 ·········································|································|·································
 |  StringUtilsMock                       ·                 2.108 (0.000)  ·                 2.140 (0.000)  │
 ·········································|································|·································
 |  IBCTokenMock                          ·                2.817 (+0.004)  ·                2.849 (+0.004)  │
 ·········································|································|·································
 |  TokenMock                             ·                 2.941 (0.000)  ·                 2.973 (0.000)  │
 ·········································|································|·································
 |  Oracle                                ·                 3.229 (0.000)  ·                 3.261 (0.000)  │
 ·········································|································|·································
 |  BusinessZoneAccountLib                ·                 3.425 (0.000)  ·                 3.481 (0.000)  │
 ·········································|································|·································
 |  IssuerLogicExecuteLib                 ·                      3.492 ()  ·                      3.549 ()  │
 ·········································|································|·································
 |  BusinessZoneAccountMock               ·                 3.543 (0.000)  ·                 3.574 (0.000)  │
 ·········································|································|·································
 |  IBCMockApp                            ·                 3.550 (0.000)  ·                 3.755 (0.000)  │
 ·········································|································|·································
 |  IBCHandlerMock                        ·                 3.868 (0.000)  ·                 3.899 (0.000)  │
 ·········································|································|·································
 |  IBCClient                             ·                 3.977 (0.000)  ·                 4.008 (0.000)  │
 ·········································|································|·································
 |  ProviderMock                          ·                 4.229 (0.000)  ·                 4.261 (0.000)  │
 ·········································|································|·································
 |  TransferProxy                         ·                 4.324 (0.000)  ·                 4.355 (0.000)  │
 ·········································|································|·································
 |  TokenStorage                          ·                      4.637 ()  ·                      4.668 ()  │
 ·········································|································|·································
 |  AccessCtrlMock                        ·                4.774 (+0.006)  ·                4.806 (+0.006)  │
 ·········································|································|·································
 |  ProviderLogicExecuteLib               ·                      5.355 ()  ·                      5.412 ()  │
 ·········································|································|·································
 |  ContractManager                       ·                5.494 (+0.608)  ·                5.525 (+0.608)  │
 ·········································|································|·································
 |  RenewableEnergyTokenLogicExecuteLib   ·                      5.885 ()  ·                      5.941 ()  │
 ·········································|································|·································
 |  FinancialZoneAccountLib               ·                6.030 (+0.061)  ·                6.087 (+0.061)  │
 ·········································|································|·································
 |  AccountMock                           ·                6.260 (+0.021)  ·                6.291 (+0.021)  │
 ·········································|································|·································
 |  RenewableEnergyTokenStorage           ·                      6.261 ()  ·                      6.292 ()  │
 ·········································|································|·································
 |  Secp256k1Curve                        ·                 6.563 (0.000)  ·                 6.595 (0.000)  │
 ·········································|································|·································
 |  ProviderStorage                       ·                      6.880 ()  ·                      6.911 ()  │
 ·········································|································|·································
 |  IBCQuerier                            ·                 7.027 (0.000)  ·                 7.059 (0.000)  │
 ·········································|································|·································
 |  ValidatorMock                         ·                7.372 (+0.813)  ·                7.403 (+0.813)  │
 ·········································|································|·································
 |  ValidatorLogicExecuteLib              ·                      7.432 ()  ·                      7.488 ()  │
 ·········································|································|·································
 |  IssuerStorage                         ·                      8.325 ()  ·                      8.356 ()  │
 ·········································|································|·································
 |  ValidatorStorage                      ·                      8.554 ()  ·                      8.585 ()  │
 ·········································|································|·································
 |  BalanceSyncBridge                     ·                8.605 (-0.022)  ·                8.637 (-0.022)  │
 ·········································|································|·································
 |  AccountSyncBridge                     ·                 9.797 (0.000)  ·                 9.828 (0.000)  │
 ·········································|································|·································
 |  RemigrationRestore                    ·                10.312 (0.000)  ·                10.343 (0.000)  │
 ·········································|································|·································
 |  ProviderLogicCallLib                  ·                     10.319 ()  ·                     10.376 ()  │
 ·········································|································|·································
 |  JPYTokenTransferBridge                ·               10.473 (+0.004)  ·               10.504 (+0.004)  │
 ·········································|································|·································
 |  MockClient                            ·                11.162 (0.000)  ·                11.366 (0.000)  │
 ·········································|································|·································
 |  OwnableIBCHandler                     ·                11.316 (0.000)  ·                11.807 (0.000)  │
 ·········································|································|·································
 |  RenewableEnergyTokenLogic             ·                     11.789 ()  ·                     11.820 ()  │
 ·········································|································|·································
 |  IBCToken                              ·               12.206 (+0.830)  ·               12.237 (+0.830)  │
 ·········································|································|·································
 |  IBCChannelPacketTimeout               ·                13.007 (0.000)  ·                13.038 (0.000)  │
 ·········································|································|·································
 |  IssuerLogicCallLib                    ·                     13.291 ()  ·                     13.348 ()  │
 ·········································|································|·································
 |  TokenLogicExecuteLib                  ·                     13.538 ()  ·                     13.595 ()  │
 ·········································|································|·································
 |  ProviderLogic                         ·                     13.626 ()  ·                     13.657 ()  │
 ·········································|································|·································
 |  TokenLogicCallLib                     ·                     13.963 ()  ·                     14.020 ()  │
 ·········································|································|·································
 |  RenewableEnergyTokenLogicCallLib      ·                     14.054 ()  ·                     14.110 ()  │
 ·········································|································|·································
 |  BusinessZoneAccount                   ·               14.343 (+0.196)  ·               14.374 (+0.196)  │
 ·········································|································|·································
 |  FinancialZoneAccount                  ·               14.533 (+0.003)  ·               14.564 (+0.003)  │
 ·········································|································|·································
 |  IBCChannelPacketSendRecv              ·                14.830 (0.000)  ·                14.861 (0.000)  │
 ·········································|································|·································
 |  TokenLogic                            ·                     15.576 ()  ·                     15.607 ()  │
 ·········································|································|·································
 |  FinancialCheck                        ·               15.656 (-0.136)  ·               15.688 (-0.136)  │
 ·········································|································|·································
 |  RemigrationBackup                     ·               17.279 (+0.030)  ·               17.311 (+0.030)  │
 ·········································|································|·································
 |  AccountLib                            ·               17.540 (+3.700)  ·               17.597 (+3.700)  │
 ·········································|································|·································
 |  AccessCtrl                            ·               18.158 (+0.771)  ·               18.189 (+0.771)  │
 ·········································|································|·································
 |  IBCChannelHandshake                   ·                18.314 (0.000)  ·                18.346 (0.000)  │
 ·········································|································|·································
 |  IssuerLogic                           ·                     18.543 ()  ·                     18.574 ()  │
 ·········································|································|·································
 |  IBCConnectionSelfStateNoValidation    ·                19.171 (0.000)  ·                19.202 (0.000)  │
 ·········································|································|·································
 |  ValidatorLogicCallLib                 ·                     19.725 ()  ·                     19.781 ()  │
 ·········································|································|·································
 |  RemigrationLib                        ·               19.792 (+0.303)  ·               19.849 (+0.303)  │
 ·········································|································|·································
 |  IBFT2Client                           ·                21.530 (0.000)  ·                21.734 (0.000)  │
 ·········································|································|·································
 |  ValidatorLogic                        ·                     23.295 ()  ·                     23.327 ()  │
 ·········································|································|·································
 |  Account                               ·               23.968 (+1.398)  ·               24.000 (+1.398)  │
 ·----------------------------------------|--------------------------------|--------------------------------·

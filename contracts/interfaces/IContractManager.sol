// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "./IAccessCtrl.sol";
import "./IProvider.sol";
import "./IProviderStorage.sol";
import "./IIssuer.sol";
import "./IIssuerStorage.sol";
import "./IValidatorStorage.sol";
import "./IValidator.sol";
import "./IAccount.sol";
import "./IFinancialZoneAccount.sol";
import "./IBusinessZoneAccount.sol";
import "./IToken.sol";
import "./ITokenStorage.sol";
import "./IIBCToken.sol";
import "./IFinancialCheck.sol";
import "./ITransferProxy.sol";
import "./IBalanceSyncBridge.sol";
import {IAccountStorage} from "./IAccountStorage.sol";

/**
 * @dev ContractManagerインターフェース
 */
interface IContractManager {
    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    function setIbcApp(
        address ibcApp,
        string memory ibcAppName,
        uint256 deadline,
        bytes memory signature
    ) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev (内部用)AccessCtrlアドレス取得
     * @return AccessCtrlアドレス
     */
    function accessCtrl() external view returns (IAccessCtrl);

    /**
     * @dev (内部用)Providerアドレス取得
     * @return Providerアドレス
     */
    function provider() external view returns (IProvider);

    /**
     * @dev providerStorge取得。
     * @return providerStorage providerStorage
     */
    function providerStorage() external view returns (IProviderStorage);

    /**
     * @dev (内部用)Issuerアドレス取得
     * @return Issuerアドレス
     */
    function issuer() external view returns (IIssuer);

    /**
     * @dev (内部用)IssuerStorageアドレス取得
     * @return IssuerStorageアドレス
     */
    function issuerStorage() external view returns (IIssuerStorage);

    /**
     * @dev (内部用)Validatorアドレス取得
     * @return Validatorアドレス
     */
    function validator() external view returns (IValidator);

    /**
     * @dev (内部用)ValidatorStorageアドレス取得
     * @return ValidatorStorageアドレス
     */
    function validatorStorage() external view returns (IValidatorStorage);

    /**
     * @dev (内部用)Accountアドレス取得
     * @return Accountアドレス
     */
    function account() external view returns (IAccount);

    /**
     * @dev (内部用)AccountStorageアドレス取得
     * @return AccountStorageアドレス
     */
    function accountStorage() external view returns (IAccountStorage);

    /**
     * @dev (内部用)FinancialZoneAccountアドレス取得
     * @return FinancialZoneAccountアドレス
     */
    function financialZoneAccount() external view returns (IFinancialZoneAccount);

    /**
     * @dev (内部用)BusinessZoneAccountアドレス取得
     * @return BusinessZoneAccountアドレス
     */
    function businessZoneAccount() external view returns (IBusinessZoneAccount);

    /**
     * @dev (内部用)Tokenアドレス取得
     * @return Tokenアドレス
     */
    function token() external view returns (IToken);

    /**
     * @dev (内部用)TokenStorageアドレス取得
     * @return TokenStorageアドレス
     */
    function tokenStorage() external view returns (ITokenStorage);

    /**
     * @dev (内部用)IBCTokenアドレス取得
     * @return IBCTokenアドレス
     */
    function ibcToken() external view returns (IIBCToken);

    /**
     * @dev (内部用)Tokenアドレス取得
     * @return Tokenアドレス
     */
    function financialCheck() external view returns (IFinancialCheck);

    /**
     * @dev (内部用)Tokenアドレス取得
     * @return TransferProxyアドレス
     */
    function transferProxy() external view returns (ITransferProxy);

    /**
     * @dev (内部用)Tokenアドレス取得
     * @return TransferProxyアドレス
     */
    function balanceSyncBridge() external view returns (IBalanceSyncBridge);

    /**
     * @dev (内部用)IBC/APPアドレス取得
     * @return IBC/APPアドレス
     */
    function ibcApp(string memory ibcAppName) external view returns (address);
}

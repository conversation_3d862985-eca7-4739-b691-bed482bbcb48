// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev AccountStorageインターフェース
 *      Accountデータのストレージ操作を定義
 *      AccountLogicコントラクトからのみ呼び出し可能
 */
interface IAccountStorage {
    ///////////////////////////////////
    // AccountData CRUD操作
    ///////////////////////////////////

    /**
     * @dev アカウントの登録
     *
     * @param key マッピングのキーとなるアカウントID
     * @param accountName アカウント名
     */
    function addAccountData(bytes32 key, string memory accountName) external;

    /**
     * @dev 発行者データを設定する
     * @param accountId 発行者ID
     * @return validatorId バリデータID
     */
    function getValidatorId(bytes32 accountId) external view returns (bytes32 validatorId);

    /**
     * @dev 発行者データを設定する
     * @param validatorId バリデータID
     * @param accountId 発行者ID
     */
    function addValidatorId(bytes32 accountId, bytes32 validatorId) external;

    /**
     * @dev 発行者のデータ名。
     * @param accountId 発行者ID
     */
    function getAccountName(bytes32 accountId) external view returns (string memory accountName);

    /**
     * @dev 発行者データの名前を更新する
     * @param accountId 発行者ID
     * @param accountName アカウント名
     */

    function updateAccountName(bytes32 accountId, string memory accountName) external;

    /**
     * @dev 発行後の残高。
     * @param accountId 発行者ID
     * @return balance 発行後の残高
     */
    function getAccountBalance(bytes32 accountId) external view returns (uint256 balance);

    /**
     * @dev Accountの有効性を更新する
     * @param accountId マッピングのキーとなるアカウントID
     * @param balance 発行後の残高
     */
    function setAccountBalance(bytes32 accountId, uint256 balance) external;

    ///////////////////////////////////
    // AccountIds配列操作
    ///////////////////////////////////

    /**
     * @dev 発行者IDを配列に追加する
     * @param accountId 追加する発行者ID
     */
    function addAccountId(bytes32 accountId) external;

    /**
     * @dev アカウントのステータスを解約済みに更新する　TODO:他関数と統合する
     * @param accountId マッピングのキーとなるアカウントID
     * @param reasonCode 理由コード
     */
    function setTerminated(bytes32 accountId, bytes32 reasonCode) external;

    /**
     * @dev 連携済みzone情報の追加
     * @param accountId マッピングのキーとなるアカウントID
     * @param zoneId zoneId
     */
    function addZone(bytes32 accountId, uint16 zoneId) external;

    /**
     * @dev アカウント許可額設定
     *
     * @param ownerId マッピングのキーとなる所有者ID
     * @param spenderId 支払い許可対象となるアカウントID
     * @param spenderName 支払い許可対象となるアカウント名
     * @param approvedAt 支払い許可日時
     * @param amount 支払い許可額
     */
    //    function setApproval(
    //        bytes32 ownerId,
    //        bytes32 spenderId,
    //        string memory spenderName,
    //        uint256 approvedAt,
    //        uint256 amount
    //    ) external;

    /**
     * @dev 残高編集
     *
     * @param accountId 発行者ID
     * @param amount 発行額
     * @param isAddition true:加算 / false:減産
     * @return balance 発行後の残高
     */
    //    function setBalance(
    //        bytes32 accountId,
    //        uint256 amount,
    //        bool isAddition
    //    ) external;

    /**
     * @dev 送金許可額の減額を行う
     *
     * @param accountId 発行者ID
     * @param spenderId 送金許可対象者のID
     * @param amount 送金許可額
     */
    //    function setAllowance(
    //        bytes32 accountId,
    //        bytes32 spenderId,
    //        uint256 amount
    //    ) external;

    ///////////////////////////////////
    // AccountIdExistence マッピング操作
    ///////////////////////////////////

    /**
     * @dev 発行者IDの存在フラグを取得する
     * @param accountId 発行者ID
     * @return exists 存在フラグ
     */
    function getAccountIdExistence(bytes32 accountId) external view returns (bool exists);

    /**
     * @dev 発行者IDの存在フラグを設定する
     * @param accountId 発行者ID
     * @param exists 存在フラグ
     */
    function addAccountIdExistence(bytes32 accountId, bool exists) external;

    /**
     * @dev アカウントステータス情報を取得する
     * @param accountId 発行者ID
     * @param accountStatus アカウントステータス
     */
    function getAccountStatus(bytes32 accountId) external view returns (bytes32 accountStatus);

    /**
     * @dev Accountの有効性を更新する(凍結 or アクティブ)
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 理由コード
     */
    function setAccountStatusAndReasonCode(
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode
    ) external;

    /**
     * @dev Accountの有効性を更新する
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     */
    function setAccountStatus(bytes32 accountId, bytes32 accountStatus) external;

    /**
     * @dev 送金許可設定。
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param approvedAt 支払い許可日時
     * @param amount 許容額
     */
    function setApprove(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 approvedAt,
        uint256 amount
    ) external;

    ///////////////////////////////////
    // バックアップ・リストア関連
    ///////////////////////////////////

    /**
     * @dev バックアップ用に全発行者データを設定する（Admin権限必要）
     * @param account 全発行者データ
     */
    function setAccountAll(AccountsAll memory account) external;

    /**
     * @dev Accountの情報を返す。
     *
     * @param accountId accountId
     * @return accountDataWithoutZoneId アカウントデータ(zoneIdなし)
     */
    function getAccountDataWithoutZoneId(bytes32 accountId)
        external
        view
        returns (AccountDataWithoutZoneId memory accountDataWithoutZoneId);

    /**
     * @dev Accountの情報を返す。
     *
     * @param accountId accountId
     * @return accountData アカウントデータ
     */
    function getAccountData(bytes32 accountId)
        external
        view
        returns (AccountData memory accountData);

    /**
     * @dev IndexよりAccountIDを取得する。
     *
     * @param index index
     * @return accountId accountId
     * @return err エラーメッセージ
     */
    function getAccountId(uint256 index)
        external
        view
        returns (bytes32 accountId, string memory err);

    /**
     * @dev アカウントの許可額を取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param index 許可対象のアカウントID
     * @return allowance
     * @return approvedAt
     */
    function getAllowance(bytes32 accountId, bytes32 index)
        external
        view
        returns (uint256 allowance, uint256 approvedAt);

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     * @param ownerId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        );

    /**
     * @dev Accountの数を返却する。
     * @param count accountの数
     */
    function getAccountCount() external view returns (uint256 count);

    /**
     * @dev アカウントに連携済みのzoneIdの取得
     * @param accountId マッピングのキーとなるアカウントID
     * @return zoneIdList アカウントに連携済みのzoneIdのリスト
     */
    function getAccountZoneIdList(bytes32 accountId)
        external
        view
        returns (uint16[] memory zoneIdList);

    /**
     * @dev limitとoffsetで指定したAccountsを一括取得する
     */
    function getAccountAll(uint256 index) external view returns (AccountsAll memory account);

    /**
     * @dev 送金許可額の減額を行う。
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 送金額
     */
    function setAllowance(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external;
}

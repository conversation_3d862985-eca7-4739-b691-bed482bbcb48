// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IAccountStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";

import "./remigration/RemigrationLib.sol";

/**
 * @dev AccountStorageコントラクト
 *      Accountデータのストレージ管理を行う
 *      CRUDのみを実装し、ビジネスロジックは含まない
 */
contract AccountStorage is Initializable, IAccountStorage {
    using RemigrationLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////
    uint256 private constant MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant EMPTY_LENGTH = 0;
    /** @dev バリデーション用のステータス値(解約済) */
    bytes32 private constant STATUS_TERMINATED = "terminated";
    /** @dev バリデーション用のステータス値(アクティブ) */
    bytes32 private constant STATUS_ACTIVE = "active";

    /** @dev AccountLogicコントラクトアドレス */
    address private _accountLogicAddr;

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev アカウントID */
    bytes32[] private _accountIds;
    /** @dev アカウントIDの存在確認フラグ(accountId => boolean) */
    mapping(bytes32 => bool) private _accountIdExistence;
    /** @dev アカウントデータ(accountId => AccountData) */
    mapping(bytes32 => AccountData) private _accountData;
    /** @dev  登録先のアカウント許可額データマッピング */
    mapping(bytes32 => AllowanceList) private _accountApprovalMapping;

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev AccountLogicコントラクトからのみ呼び出し可能を保証するmodifier
     */
    modifier accountLogicOnly() {
        require(msg.sender == _accountLogicAddr, Error.INVALID_CALLER_ADDRESS);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     * @param contractManager ContractManagerアドレス
     * @param accountLogicAddr AccountLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address accountLogicAddr)
        public
        initializer
    {
        require(address(contractManager) != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
        require(accountLogicAddr != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
        _contractManager = contractManager;
        _accountLogicAddr = accountLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev アカウントの登録
     *
     * @param key マッピングのキーとなるアカウントID
     * @param accountName アカウント名
     */
    function addAccountData(bytes32 key, string memory accountName) external accountLogicOnly {
        _accountData[key].accountName = accountName;
        _accountData[key].accountStatus = STATUS_ACTIVE;
        _accountData[key].registeredAt = block.timestamp; // 登録日時として現在のブロックタイムスタンプを設定
    }

    /**
     * @dev アカウントID登録
     * @param key 追加対象のアカウントID
     */
    function addAccountId(bytes32 key) external accountLogicOnly {
        _accountIds.push(key);
    }

    /**
     * @dev 発行者IDの存在フラグを取得する
     * @param key 発行者ID
     * @return exists 存在フラグ
     */
    function getAccountIdExistence(bytes32 key)
        external
        view
        accountLogicOnly
        returns (bool exists)
    {
        return _accountIdExistence[key];
    }

    /**
     * @dev アカウント存在確認フラグ登録
     * @param key 追加対象のアカウントID
     * @param exists true:存在 / false:削除
     */
    function addAccountIdExistence(bytes32 key, bool exists) external accountLogicOnly {
        _accountIdExistence[key] = exists;
    }

    /**
     * @dev アカウントに紐づくvalidatorIdの登録
     * @param key マッピングのキーとなるアカウントID
     */
    function addValidatorId(bytes32 key, bytes32 validatorId) external accountLogicOnly {
        _accountData[key].validatorId = validatorId;
    }

    /**
     * @dev 発行者データを設定する
     * @param key 発行者ID
     * @return validatorId バリデータID
     */
    function getValidatorId(bytes32 key)
        external
        view
        accountLogicOnly
        returns (bytes32 validatorId)
    {
        return _accountData[key].validatorId;
    }

    /**
     * @dev アカウント名の変更
     * @param key マッピングのキーとなるアカウントID
     * @param accountName アカウント名
     */
    function updateAccountName(bytes32 key, string memory accountName) external accountLogicOnly {
        _accountData[key].accountName = accountName;
    }

    /**
     * @dev アカウントステータス
     * @param key マッピングのキーとなるアカウントID
     */
    function getAccountStatus(bytes32 key)
        external
        view
        accountLogicOnly
        returns (bytes32 accountStatus)
    {
        return _accountData[key].accountStatus;
    }

    /**
     * @dev Accountの有効性を更新する(凍結 or アクティブ)
     * @param key マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 理由コード
     */
    function setAccountStatusAndReasonCode(
        bytes32 key,
        bytes32 accountStatus,
        bytes32 reasonCode
    ) external accountLogicOnly {
        _accountData[key].accountStatus = accountStatus;
        _accountData[key].reasonCode = reasonCode;
    }

    /**
     * @dev Accountの有効性を更新する(アクティブ)
     * @param key マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     */
    function setAccountStatus(bytes32 key, bytes32 accountStatus) external accountLogicOnly {
        _accountData[key].accountStatus = accountStatus;
    }

    /**
     * @dev アカウントのステータスを解約済みに更新する　TODO:他関数と統合する
     * @param key マッピングのキーとなるアカウントID
     * @param reasonCode 理由コード
     */
    function setTerminated(bytes32 key, bytes32 reasonCode) external accountLogicOnly {
        _accountData[key].accountStatus = STATUS_TERMINATED;
        _accountData[key].reasonCode = reasonCode;
        _accountData[key].terminatedAt = block.timestamp;
    }

    /**
     * @dev 連携済みzone情報の追加
     * @param key マッピングのキーとなるアカウントID
     * @param zoneId zoneId
     */
    function addZone(bytes32 key, uint16 zoneId) external accountLogicOnly {
        _accountData[key].zoneIds.push(zoneId);
    }

    /**
     * @dev アカウントステータス
     * @param key マッピングのキーとなるアカウントID
     */
    function getAccountName(bytes32 key)
        public
        view
        accountLogicOnly
        returns (string memory accountName)
    {
        return _accountData[key].accountName;
    }

    /**
     * @dev 発行後の残高。
     * @param key 発行者ID
     * @return balance 発行後の残高
     */
    function getAccountBalance(bytes32 key)
        external
        view
        accountLogicOnly
        returns (uint256 balance)
    {
        return _accountData[key].balance;
    }

    /**
     * @dev Accountの有効性を更新する(アクティブ)
     * @param key マッピングのキーとなるアカウントID
     * @param balance 発行後の残高
     */
    function setAccountBalance(bytes32 key, uint256 balance) external accountLogicOnly {
        _accountData[key].balance = balance;
    }

    /**
     * @dev 送金許可設定。
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param approvedAt 支払い許可日時
     * @param amount 許容額
     */
    function setApprove(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 approvedAt,
        uint256 amount
    ) external accountLogicOnly {
        _accountApprovalMapping[ownerId].spender.push(spenderId);
        _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAmount = amount;
        _accountApprovalMapping[ownerId]
            .accountApprovalData[spenderId]
            .spenderAccountName = getAccountName(spenderId);
        _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAt = approvedAt;
    }

    /**
     * @dev 送金許可額の減額を行う。
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 送金額
     */
    function setAllowance(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external accountLogicOnly {
        if (
            _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAmount >=
            Constant._MAX_ALLOWANCE_VALUE
        ) return;
        require(
            _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAmount >=
                amount,
            Error.UE4401_ALLOWANCE_NOT_ENOUGH
        );
        _accountApprovalMapping[ownerId].accountApprovalData[spenderId].approvedAmount -= amount;
    }

    /**
     * @dev バックアップ用に全発行者データを設定する（Admin権限必要）
     * @param account 全発行者データ
     */
    function setAccountAll(AccountsAll memory account) external accountLogicOnly {
        RemigrationLib.setAccountAll(
            _accountIds,
            _accountData[account.accountId],
            _accountIdExistence,
            _accountApprovalMapping,
            address(_contractManager),
            account
        );
    }

    /**
     * @dev Accountの情報を返す。
     *
     * @param key 発行者ID
     * @return accountDataWithoutZoneId アカウントデータ(zoneIdなし)
     */
    function getAccountDataWithoutZoneId(bytes32 key)
        external
        view
        accountLogicOnly
        returns (AccountDataWithoutZoneId memory accountDataWithoutZoneId)
    {
        accountDataWithoutZoneId = AccountDataWithoutZoneId(
            _accountData[key].accountName,
            _accountData[key].accountStatus,
            _accountData[key].balance,
            _accountData[key].reasonCode,
            _accountData[key].appliedAt,
            _accountData[key].registeredAt,
            _accountData[key].terminatingAt,
            _accountData[key].terminatedAt
        );
        return accountDataWithoutZoneId;
    }

    function getAccountData(bytes32 key)
        external
        view
        accountLogicOnly
        returns (AccountData memory accountData)
    {
        accountData = _accountData[key];
        return accountData;
    }

    /**
     * @dev IndexよりAccountIDを取得する。
     *
     * @param index index
     * @return accountId accountId
     */
    function getAccountId(uint256 index)
        external
        view
        accountLogicOnly
        returns (bytes32 accountId, string memory err)
    {
        if (_accountIds.length <= index) {
            return (0x00, Error.UE0106_ACCOUNT_OUT_OF_INDEX);
        }
        return (_accountIds[index], "");
    }

    /**
     * @dev アカウントの許可額を取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param index 許可対象のアカウントID
     * @return allowance
     * @return approvedAt
     */
    function getAllowance(bytes32 accountId, bytes32 index)
        external
        view
        accountLogicOnly
        returns (uint256 allowance, uint256 approvedAt)
    {
        return (
            _accountApprovalMapping[accountId].accountApprovalData[index].approvedAmount,
            _accountApprovalMapping[accountId].accountApprovalData[index].approvedAt
        );
    }

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     * @param ownerId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        accountLogicOnly
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        )
    {
        (bool success, string memory error) = _contractManager.account().hasAccount(ownerId);
        if (!success) {
            return (new AccountApprovalAll[](0), 0, error);
        }

        bytes32[] memory spenders = _accountApprovalMapping[ownerId].spender;
        AccountApprovalAll[] memory accountApprovalList = new AccountApprovalAll[](spenders.length);

        if (limit == 0 || spenders.length == 0) {
            return (accountApprovalList, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (accountApprovalList, EMPTY_LENGTH, Error.UE0107_ACCOUNT_TOO_LARGE_LIMIT);
        }
        if (offset >= spenders.length) {
            return (accountApprovalList, EMPTY_LENGTH, Error.UE0108_ACCOUNT_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (spenders.length >= offset + limit) ? limit : spenders.length - offset;

        accountApprovalList = new AccountApprovalAll[](size);

        for (uint256 i = 0; i < size; i++) {
            accountApprovalList[i].spanderId = spenders[offset + i];
            accountApprovalList[i].spenderAccountName = _accountApprovalMapping[ownerId]
                .accountApprovalData[spenders[offset + i]]
                .spenderAccountName;
            accountApprovalList[i].allowanceAmount = _accountApprovalMapping[ownerId]
                .accountApprovalData[spenders[offset + i]]
                .approvedAmount;
            accountApprovalList[i].approvedAt = _accountApprovalMapping[ownerId]
                .accountApprovalData[spenders[offset + i]]
                .approvedAt;
        }

        return (accountApprovalList, spenders.length, "");
    }

    /**
     * @dev Accountの数を返却する。
     * @param count accountの数
     */
    function getAccountCount() external view accountLogicOnly returns (uint256 count) {
        return _accountIds.length;
    }

    /**
     * @dev アカウントに連携済みのzoneIdの取得
     * @param key マッピングのキーとなるアカウントID
     * @return zoneIdList アカウントに連携済みのzoneIdのリスト
     */
    function getAccountZoneIdList(bytes32 key)
        external
        view
        accountLogicOnly
        returns (uint16[] memory zoneIdList)
    {
        return _accountData[key].zoneIds;
    }

    /**
     * @dev limitとoffsetで指定したAccountsを一括取得する
     */
    function getAccountAll(uint256 index) external view returns (AccountsAll memory account) {
        bytes32 _accountId = _accountIds[index];

        return
            RemigrationLib.getAccountAll(
                _accountData[_accountId],
                _accountIdExistence,
                _accountApprovalMapping,
                address(_contractManager),
                _accountId
            );
    }
}

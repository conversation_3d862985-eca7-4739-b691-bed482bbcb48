// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";

import "./remigration/RemigrationLib.sol";
import "./interfaces/IAccountStorage.sol";
import "./libraries/AccountLogicCallLib.sol";
import "./libraries/AccountLogicExecuteLib.sol";

/**
 * @dev AccountLogicコントラクト
 */
contract AccountLogic is Initializable, IAccount {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev AccountStorageアドレス */
    IAccountStorage private _accountStorage;

    /** @dev バリデーション用のステータス値(アクティブ) */
    bytes32 private constant STATUS_ACTIVE = "active";
    /** @dev バリデーション用のステータス値(凍結) */
    bytes32 private constant STATUS_FROZEN = "frozen";
    /* @dev setAccountsAllのsignature検証用 */
    string private constant SET_ACCOUNTS_ALL_SIGNATURE = "setAccountsAll";

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * * @param accountStorage AccountStorageコントラクト参照
     */
    function initialize(IContractManager contractManager, IAccountStorage accountStorage)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && address(accountStorage) != address(0),
            Error.RV0007_ACCOUNT_INVALID_VAL
        );
        _contractManager = contractManager;
        _accountStorage = accountStorage;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return version コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.ACCOUNT_NOT_ADMIN);
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev 共通領域 アカウント登録。
     *
     * @param accountId accountId
     * @param accountName アカウント名
     */

    function addAccount(
        bytes32 accountId,
        string memory accountName,
        bytes32 validatorId
    ) external override {
        AccountLogicCallLib.checkAddAccountIsValid(_contractManager, _accountStorage, accountId);
        AccountLogicExecuteLib.executeAddAccount(
            _accountStorage,
            accountId,
            accountName,
            validatorId
        );
    }

    /**
     * @dev アカウント名変更
     * @param accountId accountId
     * @param accountName アカウント名
     * @param traceId traceId
     */
    function modAccount(
        bytes32 accountId,
        string memory accountName,
        bytes32 traceId
    ) external override {
        AccountLogicCallLib.checkValidator(_contractManager);
        AccountLogicExecuteLib.updateAccountName(_accountStorage, accountId, accountName);
        emit ModAccount(accountId, accountName, traceId);
    }

    /**
     * @dev AccountのRoleを追加する。
     * ```
     * emit event: AddAccountRole()
     * ```
     *
     * @param accountId accountId
     * @param accountEoa accountEoa
     * @param traceId トレースID
     */
    function addAccountRole(
        bytes32 accountId,
        address accountEoa,
        bytes32 traceId
    ) external override {
        AccountLogicCallLib.checkAddAccountRoleIsValid(_contractManager, accountEoa);
        AccountLogicCallLib.checkHasAccount(_accountStorage, accountId);

        // EOAを追加する
        _contractManager.accessCtrl().addAccountEoa(accountId, accountEoa);

        // Emit Event
        emit AddAccountRole(accountId, accountEoa, traceId);
    }

    /**
     * @dev Accountの有効性を更新する。(アクティブ or 凍結)
     * ```
     * emit event: AccountEnabled()
     * ```
     *
     * @param accountId accountId
     * @param accountStatus アカウントステータス
     * @param reasonCode reasonCode
     */
    function setAccountStatus(
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode,
        bytes32 traceId
    ) external override {
        AccountLogicCallLib.checkAccountStatusIsValid(
            _accountStorage,
            _contractManager,
            msg.sender,
            accountId,
            accountStatus
        );
        AccountLogicExecuteLib.setAccountStatusAndReasonCode(
            _accountStorage,
            accountId,
            accountStatus,
            reasonCode
        );
        emit AccountEnabled(accountId, accountStatus, reasonCode, traceId);
    }

    /**
     * @dev Accountの解約
     * @param accountId accountId
     * @param reasonCode reasonCode
     * @param traceId トレースID
     */
    function setTerminated(
        bytes32 accountId,
        bytes32 reasonCode,
        bytes32 traceId
    ) external override {
        AccountLogicCallLib.checkTerminatedIsValid(_contractManager, accountId);
        AccountLogicExecuteLib.setTerminated(_accountStorage, accountId, reasonCode);

        emit AccountTerminated(accountId, reasonCode, traceId);
    }

    /**
     * @dev 連携済みzone情報の追加
     * @param accountId accountId
     * @param zoneId zoneId
     * @param traceId トレースID
     */
    function addZone(
        bytes32 accountId,
        uint16 zoneId,
        bytes32 traceId
    ) external override {
        //Account存在確認
        AccountLogicCallLib.checkHasAccount(_accountStorage, accountId);
        AccountLogicExecuteLib.addZone(_accountStorage, accountId, zoneId);

        emit AddZone(accountId, zoneId, traceId);
    }

    /**
     * @dev 送金許可設定。
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 許容額
     */
    function approve(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external override {
        AccountLogicCallLib.checkToken(_contractManager);
        AccountLogicExecuteLib.setApprove(
            _accountStorage,
            ownerId,
            spenderId,
            _contractManager.financialZoneAccount().getJSTDay(),
            amount
        );
    }

    /**
     * @dev 発行。
     * @param accountId accountId
     * @param amount mint額
     * @return balance mint後の残高
     */
    function mint(bytes32 accountId, uint256 amount) external override returns (uint256) {
        // Tokenコントラクトからの呼び出しである事が条件
        AccountLogicCallLib.checkToken(_contractManager);
        // 呼び出し元のTokenで残高をemitするため、残高の更新とreturnを行う
        return AccountLogicExecuteLib.setBalance(_accountStorage, accountId, amount, true);
    }

    /**
     * @dev 償却。
     * @param accountId accountId
     * @param amount burn額
     * @return balance burn後の残高
     */
    function burn(bytes32 accountId, uint256 amount) external override returns (uint256) {
        // Tokenコントラクトからの呼び出しである事が条件
        AccountLogicCallLib.checkToken(_contractManager);
        // 呼び出し元のTokenで残高をemitするため、残高の更新とreturnを行う
        return AccountLogicExecuteLib.setBalance(_accountStorage, accountId, amount, false);
    }

    /**
     * @dev Accountの残高を強制償却する
     * 最初にアカウントの凍結状態をisFrozenで確認し、凍結状態でない場合はエラーを返す
     * @param accountId accountId
     * @param traceId traceId
     */
    function forceBurn(bytes32 accountId, bytes32 traceId) external override {
        // 全残高情報と合計値を取得
        // （Issuerコントラクトからの呼び出し権限をチェックするgetAllBalanceWithAuthを使用）
        AccountLogicCallLib.checkIssuer(_contractManager);
        (, uint256 totalBalance) = AccountLogicCallLib.getAllBalance(
            _accountStorage,
            _contractManager,
            accountId
        );

        // Accountの残高をBizZone含め全て強制償却する
        (
            uint256 burnedAmount,
            uint256 burnedBalance,
            ForceDischarge[] memory forceDischarge
        ) = AccountLogicExecuteLib.forceBurn(_accountStorage, _contractManager, accountId);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (bytes32 validatorId, ) = AccountLogicCallLib.getValidatorIdByAccountId(
            _accountStorage,
            accountId
        );

        // 取引後残高連携
        this.emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);

        emit ForceBurn(
            validatorId,
            accountId,
            traceId,
            totalBalance,
            burnedAmount,
            burnedBalance,
            forceDischarge
        );
    }

    /**
     * @dev AccountのBalanceを部分的に強制償却
     * @param accountId accountId
     * @param burnedAmount 償却する金額
     * @param burnedBalance 償却後に残す金額
     * @param traceId traceId
     */
    function partialForceBurn(
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        bytes32 traceId
    ) external override {
        // 全残高情報と合計値を取得
        // （Issuerコントラクトからの呼び出し権限をチェックするgetAllBalanceWithAuthを使用）
        AccountLogicCallLib.checkIssuer(_contractManager);
        (, uint256 totalBalance) = AccountLogicCallLib.getAllBalance(
            _accountStorage,
            _contractManager,
            accountId
        );

        // Accountの残高を部分的に強制償却する
        ForceDischarge[] memory forceDischarge = AccountLogicExecuteLib.partialForceBurn(
            _contractManager,
            _accountStorage,
            accountId,
            burnedAmount,
            burnedBalance
        );

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (bytes32 validatorId, ) = AccountLogicCallLib.getValidatorIdByAccountId(
            _accountStorage,
            accountId
        );

        // 取引後残高連携
        this.emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);

        emit ForceBurn(
            validatorId,
            accountId,
            traceId,
            totalBalance,
            burnedAmount,
            burnedBalance,
            forceDischarge
        );
    }

    /**
     * @dev FromからToへ送金指示。
     * @param fromAccount 送金元AccountのID
     * @param toAccount 送金先AccountのID
     * @param amount 送金額
     * @return fromAccountBalance 送金元アカウントの残高
     * @return toAccountBalance 送金先アカウントの残高
     */
    function calcBalance(
        bytes32 fromAccount,
        bytes32 toAccount,
        uint256 amount
    ) external override returns (uint256 fromAccountBalance, uint256 toAccountBalance) {
        //Tokenのコントラクトではないとエラー
        AccountLogicCallLib.checkTokenAndIBCToken(_contractManager);
        // 取引明細用にBalanceを返却
        return (
            AccountLogicExecuteLib.setBalance(_accountStorage, fromAccount, amount, false),
            AccountLogicExecuteLib.setBalance(_accountStorage, toAccount, amount, true)
        );
    }

    /**
     * @dev 送金許可額の減額を行う。
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 送金額
     */
    function calcAllowance(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external override {
        //Tokenのコントラクトではないとエラー
        AccountLogicCallLib.checkToken(_contractManager);
        // allowanceの減額を行う
        AccountLogicExecuteLib.setAllowance(_accountStorage, ownerId, spenderId, amount);
    }

    /**
     * @dev IssueVoucher, RedeemVoucherから呼ばれる残高の更新。
     * @param accountId accountId
     * @param amount 残高の更新額
     * @param calcPattern 1:残高に加算 2:残高より減算
     * @return balance 更新後の残高
     */
    function editBalance(
        bytes32 accountId,
        uint256 amount,
        uint256 calcPattern
    ) external override returns (uint256 balance) {
        //Tokenのコントラクトではないとエラー
        AccountLogicCallLib.checkTokenAndIBCToken(_contractManager);

        //issuerVoucher
        if (calcPattern == 1) {
            return AccountLogicExecuteLib.setBalance(_accountStorage, accountId, amount, true);
        }
        //redeemVoucher
        else if (calcPattern == 2) {
            return AccountLogicExecuteLib.setBalance(_accountStorage, accountId, amount, false);
        }
        //resetVoucher
        else if (calcPattern == 3) {
            return
                AccountLogicExecuteLib.setBalance(
                    _accountStorage,
                    accountId,
                    AccountLogicCallLib.getAccountBalance(_accountStorage, accountId),
                    false
                );
        }
        // 取引明細用にBalanceを返却
        return AccountLogicCallLib.getAccountBalance(_accountStorage, accountId);
    }

    /**
     * @dev 指定されたAccountIdに紐づくAccount情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     * @param account 全発行者データ
     */
    function setAccountAll(
        AccountsAll memory account,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(SET_ACCOUNTS_ALL_SIGNATURE, deadline));
            _adminOnly(hash, deadline, signature);
        }

        AccountLogicExecuteLib.setAccountAll(_accountStorage, account);
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev AccountID存在確認。
     *
     * @param accountId accountId
     * @return success true:未登録, false:登録済
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return _hasAccount(accountId);
    }

    /**
     * @dev Account有効状態確認
     *
     * @param accountId accountId
     * @return success true:有効, false:無効、凍結済み
     */
    function isActivated(bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        (success, err) = _hasAccount(accountId);
        if (!success) {
            return (false, err);
        }
        return AccountLogicCallLib.isActivated(_accountStorage, accountId);
    }

    /**
     * @dev  Accountの解約フラグの確認をする。
     *
     * @param accountId accountId
     * @return terminated true:アカウントが解約済, false:アカウントが未解約
     * @return err エラーメッセージ
     */
    // TODO terminated ではなくaccountStatus?
    function isTerminated(bytes32 accountId)
        external
        view
        override
        returns (bool terminated, string memory err)
    {
        // Account存在確認
        {
            bool success;
            (success, err) = _hasAccount(accountId);
            if (!success) {
                return (false, err);
            }
        }
        return (AccountLogicCallLib.isTerminated(_accountStorage, accountId), "");
    }

    /**
     * @dev AccountID存在確認 本体(内部関数)。
     *
     * @param accountId accountId
     * @return success true:未登録, false:登録済
     * @return err エラーメッセージ
     */
    function _hasAccount(bytes32 accountId)
        internal
        view
        returns (bool success, string memory err)
    {
        return AccountLogicCallLib.hasAccount(_accountStorage, accountId);
    }

    /**
     * @dev 残高の取得。
     *
     * @param accountId accountId
     * @return balance 残高
     * @return err エラーメッセージ
     */
    function balanceOf(bytes32 accountId)
        external
        view
        override
        returns (uint256 balance, string memory err)
    {
        if (
            msg.sender != address(_contractManager.issuer()) &&
            msg.sender != address(_contractManager.ibcToken())
        ) {
            return (0, Error.INVALID_CALLER_ADDRESS);
        }
        bool success;
        (success, err) = _hasAccount(accountId);
        if (!success) {
            return (0, err);
        }
        return (AccountLogicCallLib.getAccountBalance(_accountStorage, accountId), "");
    }

    /**
     * @dev Accountの情報を返す。
     *
     * @param accountId accountId
     * @return accountData アカウントデータ(zoneIdなし)
     * @return err
     */
    function getAccount(bytes32 accountId)
        external
        view
        override
        returns (AccountDataWithoutZoneId memory accountData, string memory err)
    {
        bool success;
        (success, err) = _hasAccount(accountId);
        (accountData, ) = AccountLogicCallLib.getAccountDataWithoutZoneId(
            _accountStorage,
            accountId,
            success,
            err
        );
        return (accountData, err);
    }

    /**
     * @dev 移転先のアカウント情報を取得する
     *
     * @param accountId アカウントID
     * @return accountName アカウント名
     * @return err エラー
     */
    function getDestinationAccount(bytes32 accountId)
        external
        view
        override
        returns (string memory accountName, string memory err)
    {
        // validatorコントラクトからの呼び出しである事が条件
        AccountLogicCallLib.checkValidator(_contractManager);

        AccountDataWithoutZoneId memory accountData;

        (accountData, err) = this.getAccount(accountId);
        if (bytes(err).length != 0) {
            return ("", err);
        }

        if (
            accountData.accountStatus != STATUS_ACTIVE && accountData.accountStatus != STATUS_FROZEN
        ) {
            return ("", Error.GE2005_ACCOUNT_DISABLED);
        }

        return (accountData.accountName, "");
    }

    /**
     * @dev Accountの全情報を返す。
     *
     * @param accountId accountId
     * @return accountDataAll アカウントデータ
     * @return err
     */
    function getAccountAll(bytes32 accountId)
        external
        view
        override
        returns (AccountDataAll memory accountDataAll, string memory err)
    {
        // validatorコントラクトからの呼び出しである事が条件
        AccountLogicCallLib.checkValidator(_contractManager);

        accountDataAll = AccountLogicCallLib.getAccountDataAll(
            _accountStorage,
            _contractManager,
            accountId
        );

        return (accountDataAll, "");
    }

    /**
     * @dev アカウントに紐づくバリデータIDを取得する
     *
     * @param accountId アカウントID
     * @return validatorId バリデータID
     * @return err エラー
     */
    function getValidatorIdByAccountId(bytes32 accountId)
        external
        view
        override
        returns (bytes32 validatorId, string memory err)
    {
        bool success;
        (success, err) = _hasAccount(accountId);
        if (!success) {
            return (0x00, err);
        }
        (bytes32 id, ) = AccountLogicCallLib.getValidatorIdByAccountId(_accountStorage, accountId);
        return (id, "");
    }

    /**
     * @dev IndexよりAccountIDを取得する。
     *
     * @param index index
     * @return accountId accountId
     * @return err エラーメッセージ
     */
    function getAccountId(uint256 index)
        external
        view
        override
        returns (bytes32 accountId, string memory err)
    {
        return AccountLogicCallLib.getAccountId(_accountStorage, index);
    }

    /**
     * @dev アカウントの限度額を取得
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountLimitData
     * @return err
     */
    function getAccountLimit(bytes32 validatorId, bytes32 accountId)
        external
        view
        override
        returns (FinancialZoneAccountData memory accountLimitData, string memory err)
    {
        // Validatorコントラクトからの呼び出しである事が条件 -> Validatorコントラクトのstack too depp回避のため削除
        // require(msg.sender == address(_contractManager.validator()), Error.NOT_VALIDATOR_CONTRACT);
        // Validatorの有効性を確認
        (bool success, string memory errHasValidator) = _contractManager.validator().hasValidator(
            validatorId
        );
        if (!success) {
            return (accountLimitData, errHasValidator);
        }

        return _contractManager.financialZoneAccount().getAccountLimitData(accountId);
    }

    /**
     * @dev 送金許可設定の取得。
     *
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @return allowance
     * @return approvedAt
     * @return err
     */
    function getAllowance(bytes32 ownerId, bytes32 spenderId)
        external
        view
        override
        returns (
            uint256 allowance,
            uint256 approvedAt,
            string memory err
        )
    {
        // TokenコントラクトかFinancialCheckコントラクトからの呼び出しである事が条件
        AccountLogicCallLib.checkValidatorAndFinance(_contractManager);
        //Account存在確認
        {
            (bool successOwner, ) = _hasAccount(ownerId);
            (bool successSpender, ) = _hasAccount(spenderId);
            if (!successOwner) return (0, 0, Error.UE0104_OWNER_NOT_EXIST);
            else if (!successSpender) return (0, 0, Error.UE0105_SPENDER_NOT_EXIST);
        }
        // allowanceを返す
        (allowance, approvedAt) = AccountLogicCallLib.getAllowance(
            _accountStorage,
            ownerId,
            spenderId
        );
        return (allowance, approvedAt, "");
    }

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     *
     * @param ownerId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        override
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        )
    {
        // tokenコントラクトからの呼び出しである事が条件
        AccountLogicCallLib.checkToken(_contractManager);

        return AccountLogicCallLib.getAllowanceList(_accountStorage, ownerId, offset, limit);
    }

    /**
     * @dev Accountの数を返却する。
     *
     * @param count accountの数
     */
    function getAccountCount() external view override returns (uint256 count) {
        return AccountLogicCallLib.getAccountCount(_accountStorage);
    }

    /**
     * @dev Accountの連携済みのzoneIdを返却する。
     *
     * @param accountId accountId
     * @return zones zoneIdのリスト
     */
    function getZoneByAccountId(bytes32 accountId)
        external
        view
        override
        returns (ZoneData[] memory zones)
    {
        uint16[] memory zoneIds = AccountLogicCallLib.getAccountZoneIdList(
            _accountStorage,
            accountId
        );
        bytes32[] memory emptyIssuerIds;

        zones = new ZoneData[](zoneIds.length);
        for (uint256 i = 0; i < zoneIds.length; i++) {
            // ZoneコントラクトからzoneNameを取得
            string memory zoneName = _contractManager.provider().getZoneName(zoneIds[i]);

            // 取得したzoneIdとzoneNameでZoneDataを作成し、配列に追加
            zones[i] = ZoneData(zoneIds[i], zoneName, emptyIssuerIds);
        }

        return zones;
    }

    /**
     * @dev Accountが凍結状態となっているか確認する。
     *
     * @param accountId accountId
     * @return frozen 凍結状態
     * @return err エラーメッセージ
     */
    function isFrozen(bytes32 accountId)
        external
        view
        override
        returns (bool frozen, string memory err)
    {
        //アカウントの存在確認
        (bool success, string memory errTmp) = _hasAccount(accountId);
        if (!success) {
            return (false, errTmp);
        }
        // アカウントの凍結状態を確認
        return (AccountLogicCallLib.isFrozen(_accountStorage, accountId), "");
    }

    /**
     * @dev limitとoffsetで指定したAccountsを一括取得する
     *
     */
    function getAccountsAll(uint256 index)
        external
        view
        override
        returns (AccountsAll memory account)
    {
        return AccountLogicCallLib.getAccountAll(_accountStorage, index);
    }

    /**
     * @dev 残高照会後のイベントを発行する
     * @notice アカウントの残高情報を含むイベントを発行し、監査証跡を残す
     *         未登録アカウントの場合でもエラーにならず、空の残高情報でイベントを発行する
     *
     * @param fromAccountId 送金元アカウントID（照会対象）
     * @param toAccountId 送金先アカウントID（照会対象）
     * @param traceId トレースID（イベントの追跡用）
     *
     * @custom:event AfterBalanceEmitted 以下の情報を含むイベントを発行:
     *   - fromBalance: 送金元アカウントの全ゾーン残高（FinZone→BizZone昇順）
     *   - toBalance: 送金先アカウントの全ゾーン残高（FinZone→BizZone昇順）
     *   - traceId: indexed属性付きのトレースID
     */
    function emitAfterBalance(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 traceId
    ) external override {
        // イベントの発行
        (AllBalanceData[] memory fromBalance, ) = AccountLogicCallLib.getAllBalance(
            _accountStorage,
            _contractManager,
            fromAccountId
        );
        (AllBalanceData[] memory toBalance, ) = AccountLogicCallLib.getAllBalance(
            _accountStorage,
            _contractManager,
            toAccountId
        );
        emit AfterBalance(fromBalance, toBalance, traceId);
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}

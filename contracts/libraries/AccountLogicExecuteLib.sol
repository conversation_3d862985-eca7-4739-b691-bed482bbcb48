// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import {IContractManager} from "../interfaces/IContractManager.sol";
import {IAccountStorage} from "../interfaces/IAccountStorage.sol";
import {AccountData, ForceDischarge, AccountsAll} from "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev AccountLogicExecuteLibライブラリ
 *      Accountの実行関数を実装するヘルパーライブラリ
 */

library AccountLogicExecuteLib {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev バリデーション用のステータス値(強制償却済) */
    bytes32 private constant STATUS_FORCE_BURNED = "force_burned";

    /**
     * @dev Accountの追加処理を実行する
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountName account名
     */
    function executeAddAccount(
        IAccountStorage accountStorage,
        bytes32 accountId,
        string memory accountName,
        bytes32 validatorId
    ) external {
        accountStorage.addAccountId(accountId);
        accountStorage.addAccountIdExistence(accountId, true);

        accountStorage.addAccountData(accountId, accountName);
        accountStorage.addValidatorId(accountId, validatorId);
    }

    /**
     * @dev validatorIdにaccountを紐付ける処理を実行する
     * @param accountStorage AccountStorageコントラクト参照
     * @param validatorId validatorId
     * @param accountId accountId
     */
    function executeAddValidatorId(
        IAccountStorage accountStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external {
        accountStorage.addValidatorId(accountId, validatorId);
    }

    /**
     * @dev アカウント名の変更
     *
     * @param accountId accountId
     * @param accountName アカウント名
     */
    function updateAccountName(
        IAccountStorage accountStorage,
        bytes32 accountId,
        string memory accountName
    ) external {
        accountStorage.updateAccountName(accountId, accountName);
    }

    /**
     * @dev Accountの有効性を更新する(凍結 or アクティブ)
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 理由コード
     */
    function setAccountStatusAndReasonCode(
        IAccountStorage accountStorage,
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode
    ) external {
        accountStorage.setAccountStatusAndReasonCode(accountId, accountStatus, reasonCode);
    }

    /**
     * @dev 送金許可設定。
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param approvedAt 支払い許可日時
     * @param amount 許容額
     */
    function setApprove(
        IAccountStorage accountStorage,
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 approvedAt,
        uint256 amount
    ) external {
        accountStorage.setApprove(ownerId, spenderId, approvedAt, amount);
    }

    /**
     * @dev アカウントのステータスを解約済みに更新する　TODO:他関数と統合する
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param reasonCode 理由コード
     */
    function setTerminated(
        IAccountStorage accountStorage,
        bytes32 accountId,
        bytes32 reasonCode
    ) external {
        accountStorage.setTerminated(accountId, reasonCode);
    }

    /**
     * @dev 連携済みzone情報の追加
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     * @param zoneId zoneId
     */

    function addZone(
        IAccountStorage accountStorage,
        bytes32 accountId,
        uint16 zoneId
    ) external {
        accountStorage.addZone(accountId, zoneId);
    }

    /**
     * @dev 残高編集
     * @param accountId マッピングのキーとなるアカウントID
     * @param amount 発行額
     * @param isAddition true:加算 / false:減産
     * @return balance 発行後の残高
     */
    function setBalance(
        IAccountStorage accountStorage,
        bytes32 accountId,
        uint256 amount,
        bool isAddition
    ) external returns (uint256 balance) {
        uint256 accountBalance = accountStorage.getAccountBalance(accountId);
        if (isAddition) {
            accountBalance += amount;
            accountStorage.setAccountBalance(accountId, accountBalance);
            return accountBalance;
        } else {
            require(accountBalance >= amount, Error.UE4403_TOKEN_BALANCE_NOT_ENOUGH);
            accountBalance -= amount;
            accountStorage.setAccountBalance(accountId, accountBalance);
            return accountBalance;
        }
    }

    /**
     * @dev アカウントを強制償却させる
     * @param accountId マッピングのキーとなるアカウントID
     * @return burnedAmount 償却額
     */
    function forceBurn(
        IAccountStorage accountStorage,
        IContractManager contractManager,
        bytes32 accountId
    )
        external
        returns (
            uint256 burnedAmount,
            uint256 burnedBalance,
            ForceDischarge[] memory forceDischarge
        )
    {
        string memory err;
        bool frozen;
        uint256 bizZoneBurnedAmount;

        // Accountの凍結状態確認
        (frozen, err) = contractManager.account().isFrozen(accountId);
        require(bytes(err).length == 0, err);
        require(frozen, Error.ACCOUNT_NOT_FROZEN);

        (bizZoneBurnedAmount, forceDischarge) = contractManager
            .businessZoneAccount()
            .forceBurnAllBalance(accountId);

        uint256 balance = accountStorage.getAccountBalance(accountId);

        // finの残高分totalSupplyを減額する
        contractManager.token().subTotalSupply(balance);

        burnedAmount = balance + bizZoneBurnedAmount;
        accountStorage.setAccountBalance(accountId, 0);
        accountStorage.setAccountStatus(accountId, STATUS_FORCE_BURNED);

        return (burnedAmount, accountStorage.getAccountBalance(accountId), forceDischarge);
    }

    /**
     * @dev アカウントを部分的に強制償却させる
     * @param contractManager ContractManager リファレンス
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @param burnedAmount 償却する金額
     * @param burnedBalance 償却後に残す金額
     * @return forceDischarge ディスチャージしたBizゾーン情報
     */
    function partialForceBurn(
        IContractManager contractManager,
        IAccountStorage accountStorage,
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance
    ) external returns (ForceDischarge[] memory forceDischarge) {
        string memory err;
        bool frozen;
        ForceDischarge[] memory emptyForceDischarge;

        // Accountの凍結状態確認
        (frozen, err) = contractManager.account().isFrozen(accountId);
        require(bytes(err).length == 0, err);
        require(frozen, Error.ACCOUNT_NOT_FROZEN);
        uint256 balance = accountStorage.getAccountBalance(accountId);

        // Fin Account 残高が指定した償却額以上かチェック
        if (balance >= burnedAmount) {
            // 現在の残高からburnedAmountを減額した値とburnedBalanceが一致するか検証
            uint256 expectedBalance = balance - burnedAmount;
            require(expectedBalance == burnedBalance, Error.ACCOUNT_INVALID_BURNED_BALANCE);

            accountStorage.setAccountBalance(accountId, burnedBalance);

            // totalSupplyの更新
            contractManager.token().subTotalSupply(burnedAmount);

            return (emptyForceDischarge);
        } else {
            uint256 bizZoneBurnedAmount;
            (bizZoneBurnedAmount, forceDischarge) = contractManager
                .businessZoneAccount()
                .forceBurnAllBalance(accountId);

            uint256 totalBalance = balance + bizZoneBurnedAmount;

            // 合計残高が指定した償却額より少ないか等しい場合はエラー
            require(totalBalance > burnedAmount, Error.ACCOUNT_INVALID_BURNED_AMOUNT);

            // 期待される残高を計算し、burnedBalanceと一致するか検証
            uint256 expectedBalance = totalBalance - burnedAmount;
            require(expectedBalance == burnedBalance, Error.ACCOUNT_INVALID_BURNED_BALANCE);

            accountStorage.setAccountBalance(accountId, burnedBalance);

            // tosalSupplyを減額
            contractManager.token().subTotalSupply(burnedAmount);

            return (forceDischarge);
        }
    }

    /**
     * @dev 送金許可額の減額を行う。
     * @param accountStorage AccountStorage 契約リファレンス
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 送金額
     */
    function setAllowance(
        IAccountStorage accountStorage,
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external {
        accountStorage.setAllowance(ownerId, spenderId, amount);
    }

    /**
     * @dev 指定されたAccountIdに紐づくAccount情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     * @param accountStorage AccountStorage 契約リファレンス
     * @param account 全発行者データ
     */
    function setAccountAll(IAccountStorage accountStorage, AccountsAll memory account) external {
        accountStorage.setAccountAll(account);
    }
}

// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/CountersUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/StringsUpgradeable.sol";
import "../interfaces/IContractManager.sol";
import "../interfaces/Error.sol";
import "../interfaces/RenewableEnergyTokenStruct.sol";
import "../interfaces/IRenewableEnergyToken.sol";
import "./libraries/StringUtils.sol";
import "../interfaces/ITransferable.sol";
import "./RenewableEnergyTokenStorage.sol";
import "./libraries/RenewableEnergyTokenLogicCallLib.sol";
import "./libraries/RenewableEnergyTokenLogicExecuteLib.sol";

contract RenewableEnergyTokenLogic is Initializable, IRenewableEnergyToken {
    ///////////////////////////////////
    // libraries
    ///////////////////////////////////

    using StringsUpgradeable for uint256;
    using CountersUpgradeable for CountersUpgradeable.Counter;
    using StringUtils for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev RenewableEnergyTokenStorageアドレス */
    IRenewableEnergyTokenStorage private _renewableEnergyTokenStorage;

    /** @dev Tokenコントラクトアドレス */
    ITransferable private _token;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     */
    function initialize(
        IRenewableEnergyTokenStorage renewableEnergyTokenStorage,
        ITransferable token,
        address contractManager
    ) public initializer {
        require(
            address(contractManager) != address(0) &&
                address(renewableEnergyTokenStorage) != address(0),
            Error.RV0015_RETOKEN_INVALID_VAL
        );
        _contractManager = IContractManager(contractManager);
        _token = token;
        _renewableEnergyTokenStorage = renewableEnergyTokenStorage;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev トークンを発行する
     *
     * @param tokenId トークンID
     * @param metadataId メタデータID
     * @param metadataHash メタデータハッシュ
     * @param mintAccountId ミントアカウントID
     * @param ownerAccountId オーナーアカウントID
     * @param isLocked ロック状態
     * @param traceId トレースID
     */
    function mint(
        bytes32 tokenId,
        bytes32 metadataId,
        bytes32 metadataHash,
        bytes32 mintAccountId,
        bytes32 ownerAccountId,
        bool isLocked,
        bytes32 traceId
    ) external override {
        RenewableEnergyTokenLogicCallLib.mintIsValid(_renewableEnergyTokenStorage, tokenId);
        RenewableEnergyTokenLogicExecuteLib.executeMint(
            _renewableEnergyTokenStorage,
            tokenId,
            metadataId,
            metadataHash,
            mintAccountId,
            ownerAccountId,
            isLocked
        );

        emit MintRNToken(
            tokenId,
            metadataId,
            metadataHash,
            mintAccountId,
            ownerAccountId,
            isLocked,
            traceId
        );
    }

    /**
     * @dev トークンを移転する
     *
     * @param fromAccountId 移転元アカウントID
     * @param toAccountId 移転先アカウントID
     * @param tokenId トークンID
     * @param traceId トレースID
     */
    function transfer(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 tokenId,
        bytes32 traceId
    ) external override {
        RenewableEnergyTokenLogicCallLib.transferIsValid(
            _renewableEnergyTokenStorage,
            fromAccountId,
            toAccountId,
            tokenId
        );

        RenewableEnergyTokenLogicExecuteLib.executeTransfer(
            _renewableEnergyTokenStorage,
            fromAccountId,
            toAccountId,
            tokenId
        );

        // Event用にfromAccountIdが紐づくValidatorのIdを取得する
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(
            fromAccountId
        );

        emit TransferRNToken(
            validatorId,
            fromAccountId,
            fromAccountId,
            toAccountId,
            tokenId,
            traceId
        );
    }

    /**
     * @dev トークンを移転する(カスタムトランスファー)
     *
     * @param sendAccountId SendAccount
     * @param fromAccountId FromAccount
     * @param toAccountId toAccount
     * @param amount 金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param memo メモ
     * @param traceId トレースID
     */
    function customTransfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external override returns (bool result) {
        RenewableEnergyTokenLogicCallLib.customTransferIsValid(
            _token,
            fromAccountId,
            toAccountId,
            miscValue1,
            miscValue2
        );
        if (miscValue1 == bytes32("renewable")) {
            string[] memory miscValue2Array = miscValue2.slice(",");

            return
                RenewableEnergyTokenLogicExecuteLib.executeTransferBatchTokens(
                    _renewableEnergyTokenStorage,
                    _token,
                    miscValue2Array,
                    sendAccountId,
                    fromAccountId,
                    toAccountId,
                    amount,
                    miscValue1,
                    miscValue2,
                    memo,
                    traceId
                );
        }

        return false;
    }

    /**
     * @dev RenewableEnergyTokenAll情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param renewableEnergytokens renewableEnergytokens
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function restoreRenewableEnergyTokens(
        RenewableEnergyTokenAll[] memory renewableEnergytokens,
        uint256 deadline,
        bytes memory signature
    ) external {
        RenewableEnergyTokenLogicExecuteLib.executeRestoreRenewableEnergyTokens(
            _renewableEnergyTokenStorage,
            renewableEnergytokens,
            deadline,
            signature
        );
    }

    /**
     * @dev TokenIdsByAccountIdAll情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param tokenIdsByAccountId tokenIdsByAccountId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function restoreTokenIdsByAccountId(
        TokenIdsByAccountIdAll[] memory tokenIdsByAccountId,
        uint256 deadline,
        bytes memory signature
    ) external {
        RenewableEnergyTokenLogicExecuteLib.executeRestoreTokenIdsByAccountId(
            _renewableEnergyTokenStorage,
            tokenIdsByAccountId,
            deadline,
            signature
        );
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev トークンの移転チェック
     *
     * @param fromAccountId 移転元アカウントID
     * @param toAccountId 移転先アカウントID
     * @param sendAccountId 送信元アカウントID
     * @param miscValue1 miscValue1
     * @param miscValue2 miscValue2
     */
    function checkTransaction(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 miscValue1,
        string memory miscValue2
    ) external view returns (bool success, string memory err) {
        string[] memory miscValue2Array = miscValue2.slice(",");

        return
            RenewableEnergyTokenLogicCallLib.checkTransactionIsValid(
                _renewableEnergyTokenStorage,
                address(_contractManager),
                sendAccountId,
                fromAccountId,
                toAccountId,
                miscValue1,
                miscValue2Array
            );
    }

    /**
     * @dev アカウントに紐づくトークンの一覧を取得する
     *
     * @param accountId アカウントID
     * @param offset offset
     * @param limit limit
     * @param sortOrder sortOrder(true: 降順, false: 昇順)
     * @return renewableEnergyTokenList トークンデータ一覧
     */
    function getTokenList(
        bytes32 validatorId,
        bytes32 accountId,
        uint256 offset,
        uint256 limit,
        string memory sortOrder // コントラクト側でソートを行うことは非効率なので利用しない(TODO: Coreと平仄を合わせて修正しパラメータから削除する)
    )
        external
        view
        override
        returns (
            RenewableEnergyTokenListData[] memory renewableEnergyTokenList,
            uint256 totalCount,
            string memory err
        )
    {
        return
            RenewableEnergyTokenLogicCallLib.getTokenList(
                _renewableEnergyTokenStorage,
                _contractManager,
                validatorId,
                accountId,
                offset,
                limit
            );
    }

    /**
     * @dev トークンの詳細情報を取得する
     *
     * @param tokenId トークンID
     * @return renewableEnergyTokenData トークンデータ
     */
    function getToken(bytes32 tokenId)
        external
        view
        override
        returns (RenewableEnergyTokenData memory renewableEnergyTokenData, string memory err)
    {
        return RenewableEnergyTokenLogicCallLib.getToken(_renewableEnergyTokenStorage, tokenId);
    }

    /**
     * @dev 引数のアカウントIDが、引数のトークンIDのNFTを所有しているか確認する
     *
     * @param tokenId トークンID
     * @param accountId アカウントID
     */
    function hasToken(bytes32 tokenId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err)
    {
        return
            RenewableEnergyTokenLogicCallLib.hasToken(
                _renewableEnergyTokenStorage,
                tokenId,
                accountId
            );
    }

    /**
     * @dev Tokenの数を返却する。
     *
     * @return count tokenの数
     */
    function getTokenCount() external view returns (uint256 count) {
        return RenewableEnergyTokenLogicCallLib.getTokenCount(_renewableEnergyTokenStorage);
    }

    /**
     * @dev limitとoffsetで指定したRenewableEnergyTokensを一括取得する
     *
     * @param offset オフセット
     * @param limit 取得件数
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     * @return renewableEnergyTokenAll 全RenewableEnergyTokenの情報
     * @return totalCount 取得件数
     * @return err エラーメッセージ
     */
    function backupRenewableEnergyTokens(
        uint256 offset,
        uint256 limit,
        uint256 deadline,
        bytes memory signature
    )
        external
        view
        returns (
            RenewableEnergyTokenAll[] memory renewableEnergyTokenAll,
            uint256 totalCount,
            string memory err
        )
    {
        return
            RenewableEnergyTokenLogicCallLib.backupRenewableEnergyTokens(
                _renewableEnergyTokenStorage,
                _contractManager,
                offset,
                limit,
                deadline,
                signature
            );
    }

    /**
     * @dev limitとoffsetで指定したTokenIdsByAccountIdsを一括取得する
     *
     * @param offset オフセット
     * @param limit 取得件数
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     * @return tokenIdsByAccountIdAll 全TokenIdsByAccountIdの情報
     * @return totalCount 取得件数
     * @return err エラーメッセージ
     */
    function backupTokenIdsByAccountIds(
        uint256 offset,
        uint256 limit,
        uint256 deadline,
        bytes memory signature
    )
        external
        view
        returns (
            TokenIdsByAccountIdAll[] memory tokenIdsByAccountIdAll,
            uint256 totalCount,
            string memory err
        )
    {
        return
            RenewableEnergyTokenLogicCallLib.backupTokenIdsByAccountIds(
                _renewableEnergyTokenStorage,
                _contractManager,
                offset,
                limit,
                deadline,
                signature
            );
    }

    /**
     * @dev RenewableEnergyToken全情報取得
     *      既に登録されているRenewableEnergyToken全情報取得を取得する
     *
     * @param index オフセット
     * @return renewableEnergyToken 全Tokenの情報
     */
    function getRenewableEnergyTokenAll(uint256 index)
        external
        view
        returns (RenewableEnergyTokenAll memory renewableEnergyToken)
    {
        return
            RenewableEnergyTokenLogicCallLib.getRenewableEnergyTokenAll(
                _renewableEnergyTokenStorage,
                index
            );
    }

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを取得
     * @param accountId accountId
     */
    function getTokenIdsByAccountIdAll(bytes32 accountId)
        external
        view
        returns (TokenIdsByAccountIdAll memory tokenIdsByAccountId)
    {
        return
            RenewableEnergyTokenLogicCallLib.getTokenIdsByAccountIdAll(
                _renewableEnergyTokenStorage,
                accountId
            );
    }
}
